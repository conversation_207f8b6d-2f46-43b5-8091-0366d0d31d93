import { z } from "src/zod";
export declare const EntityType: z.<PERSON><["post", "comment"]>;
export type GetPosts = z.infer<typeof GetPosts>;
export declare const GetPosts: z.ZodObject<{
    pagination: z.ZodObject<{
        page: z.Z<PERSON>efault<z.ZodNumber>;
        size: z.Z<PERSON>efault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        page: number;
        size: number;
    }, {
        page?: number | undefined;
        size?: number | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    pagination: {
        page: number;
        size: number;
    };
}, {
    pagination: {
        page?: number | undefined;
        size?: number | undefined;
    };
}>;
export type Post = z.infer<typeof Post>;
export declare const Post: z.ZodObject<{
    id: z.ZodString;
    author: z.ZodObject<{
        id: z.ZodString;
        name: z.<PERSON><z.ZodObject<{
            locale: z.<PERSON><PERSON><["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.<PERSON>ype<PERSON>ny, {
            locale: "en" | "ru";
            value: string;
        }, {
            locale: "en" | "ru";
            value: string;
        }>, "many">;
        avatar: z.ZodNullable<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        name: {
            locale: "en" | "ru";
            value: string;
        }[];
        avatar: string | null;
    }, {
        id: string;
        name: {
            locale: "en" | "ru";
            value: string;
        }[];
        avatar: string | null;
    }>;
    rating: z.ZodObject<{
        likes: z.ZodNumber;
        dislikes: z.ZodNumber;
        status: z.ZodNullable<z.ZodNativeEnum<{
            like: "like";
            dislike: "dislike";
        }>>;
    }, "strip", z.ZodTypeAny, {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    }, {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    }>;
    usefulness: z.ZodObject<{
        value: z.ZodNullable<z.ZodNumber>;
        count: z.ZodNumber;
        totalValue: z.ZodNullable<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        value: number | null;
        count: number;
        totalValue: number | null;
    }, {
        value: number | null;
        count: number;
        totalValue: number | null;
    }>;
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    body: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    tags: z.ZodArray<z.ZodString, "many">;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    tags: string[];
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
    author: {
        id: string;
        name: {
            locale: "en" | "ru";
            value: string;
        }[];
        avatar: string | null;
    };
    body: {
        locale: "en" | "ru";
        value: string;
    }[];
    rating: {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    };
    usefulness: {
        value: number | null;
        count: number;
        totalValue: number | null;
    };
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    tags: string[];
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
    author: {
        id: string;
        name: {
            locale: "en" | "ru";
            value: string;
        }[];
        avatar: string | null;
    };
    body: {
        locale: "en" | "ru";
        value: string;
    }[];
    rating: {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    };
    usefulness: {
        value: number | null;
        count: number;
        totalValue: number | null;
    };
}>;
export declare const GetPostsResponse: z.ZodObject<{
    items: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        author: z.ZodObject<{
            id: z.ZodString;
            name: z.ZodArray<z.ZodObject<{
                locale: z.ZodEnum<["en", "ru"]>;
                value: z.ZodString;
            }, "strip", z.ZodTypeAny, {
                locale: "en" | "ru";
                value: string;
            }, {
                locale: "en" | "ru";
                value: string;
            }>, "many">;
            avatar: z.ZodNullable<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            id: string;
            name: {
                locale: "en" | "ru";
                value: string;
            }[];
            avatar: string | null;
        }, {
            id: string;
            name: {
                locale: "en" | "ru";
                value: string;
            }[];
            avatar: string | null;
        }>;
        rating: z.ZodObject<{
            likes: z.ZodNumber;
            dislikes: z.ZodNumber;
            status: z.ZodNullable<z.ZodNativeEnum<{
                like: "like";
                dislike: "dislike";
            }>>;
        }, "strip", z.ZodTypeAny, {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        }, {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        }>;
        usefulness: z.ZodObject<{
            value: z.ZodNullable<z.ZodNumber>;
            count: z.ZodNumber;
            totalValue: z.ZodNullable<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            value: number | null;
            count: number;
            totalValue: number | null;
        }, {
            value: number | null;
            count: number;
            totalValue: number | null;
        }>;
        title: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            locale: "en" | "ru";
            value: string;
        }, {
            locale: "en" | "ru";
            value: string;
        }>, "many">;
        body: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            locale: "en" | "ru";
            value: string;
        }, {
            locale: "en" | "ru";
            value: string;
        }>, "many">;
        tags: z.ZodArray<z.ZodString, "many">;
        createdAt: z.ZodDate;
        updatedAt: z.ZodDate;
    }, "strip", z.ZodTypeAny, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tags: string[];
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
        author: {
            id: string;
            name: {
                locale: "en" | "ru";
                value: string;
            }[];
            avatar: string | null;
        };
        body: {
            locale: "en" | "ru";
            value: string;
        }[];
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        usefulness: {
            value: number | null;
            count: number;
            totalValue: number | null;
        };
    }, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tags: string[];
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
        author: {
            id: string;
            name: {
                locale: "en" | "ru";
                value: string;
            }[];
            avatar: string | null;
        };
        body: {
            locale: "en" | "ru";
            value: string;
        }[];
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        usefulness: {
            value: number | null;
            count: number;
            totalValue: number | null;
        };
    }>, "many">;
    total: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    items: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tags: string[];
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
        author: {
            id: string;
            name: {
                locale: "en" | "ru";
                value: string;
            }[];
            avatar: string | null;
        };
        body: {
            locale: "en" | "ru";
            value: string;
        }[];
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        usefulness: {
            value: number | null;
            count: number;
            totalValue: number | null;
        };
    }[];
    total: number;
}, {
    items: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tags: string[];
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
        author: {
            id: string;
            name: {
                locale: "en" | "ru";
                value: string;
            }[];
            avatar: string | null;
        };
        body: {
            locale: "en" | "ru";
            value: string;
        }[];
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
        usefulness: {
            value: number | null;
            count: number;
            totalValue: number | null;
        };
    }[];
    total: number;
}>;
export type CreatePost = z.infer<typeof CreatePost>;
export declare const CreatePost: z.ZodObject<{
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    body: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    tags: z.ZodArray<z.ZodString, "many">;
}, "strip", z.ZodTypeAny, {
    tags: string[];
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
    body: {
        locale: "en" | "ru";
        value: string;
    }[];
}, {
    tags: string[];
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
    body: {
        locale: "en" | "ru";
        value: string;
    }[];
}>;
export type UpdatePost = z.infer<typeof UpdatePost>;
export declare const UpdatePost: z.ZodObject<{
    title: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">>;
    body: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">>;
    tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, "strip", z.ZodTypeAny, {
    tags?: string[] | undefined;
    title?: {
        locale: "en" | "ru";
        value: string;
    }[] | undefined;
    body?: {
        locale: "en" | "ru";
        value: string;
    }[] | undefined;
}, {
    tags?: string[] | undefined;
    title?: {
        locale: "en" | "ru";
        value: string;
    }[] | undefined;
    body?: {
        locale: "en" | "ru";
        value: string;
    }[] | undefined;
}>;
export type UpdatePostRating = z.infer<typeof UpdatePostRating>;
export declare const UpdatePostRating: z.ZodObject<{
    type: z.ZodNativeEnum<{
        like: "like";
        dislike: "dislike";
    }>;
}, "strip", z.ZodTypeAny, {
    type: "like" | "dislike";
}, {
    type: "like" | "dislike";
}>;
export type PostRating = z.infer<typeof PostRating>;
export declare const PostRating: z.ZodObject<{
    likes: z.ZodNumber;
    dislikes: z.ZodNumber;
    status: z.ZodNullable<z.ZodNativeEnum<{
        like: "like";
        dislike: "dislike";
    }>>;
}, "strip", z.ZodTypeAny, {
    status: "like" | "dislike" | null;
    likes: number;
    dislikes: number;
}, {
    status: "like" | "dislike" | null;
    likes: number;
    dislikes: number;
}>;
export type UpdatePostUsefulness = z.infer<typeof UpdatePostUsefulness>;
export declare const UpdatePostUsefulness: z.ZodObject<{
    value: z.ZodNullable<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    value: number | null;
}, {
    value: number | null;
}>;
export type PostUsefulness = z.infer<typeof PostUsefulness>;
export declare const PostUsefulness: z.ZodObject<{
    value: z.ZodNullable<z.ZodNumber>;
    count: z.ZodNumber;
    totalValue: z.ZodNullable<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    value: number | null;
    count: number;
    totalValue: number | null;
}, {
    value: number | null;
    count: number;
    totalValue: number | null;
}>;
export type DeletePost = z.infer<typeof DeletePost>;
export declare const DeletePost: z.ZodObject<{
    reason: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    reason: string | null;
}, {
    reason: string | null;
}>;
export type GetCommentsEntityType = z.infer<typeof GetCommentsEntityType>;
export declare const GetCommentsEntityType: z.ZodEnum<["post"]>;
export type Comment = z.infer<typeof Comment>;
export declare const Comment: z.ZodObject<{
    id: z.ZodString;
    path: z.ZodString;
    author: z.ZodNullable<z.ZodObject<{
        id: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            locale: "en" | "ru";
            value: string;
        }, {
            locale: "en" | "ru";
            value: string;
        }>, "many">;
        avatar: z.ZodNullable<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        name: {
            locale: "en" | "ru";
            value: string;
        }[];
        avatar: string | null;
    }, {
        id: string;
        name: {
            locale: "en" | "ru";
            value: string;
        }[];
        avatar: string | null;
    }>>;
    isAnonymous: z.ZodBoolean;
    anonimityReason: z.ZodNullable<z.ZodString>;
    rating: z.ZodObject<{
        likes: z.ZodNumber;
        dislikes: z.ZodNumber;
        status: z.ZodNullable<z.ZodNativeEnum<{
            like: "like";
            dislike: "dislike";
        }>>;
    }, "strip", z.ZodTypeAny, {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    }, {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    }>;
    body: z.ZodNullable<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">>;
    childrenCount: z.ZodNumber;
    deleteReason: z.ZodNullable<z.ZodString>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    deletedAt: z.ZodNullable<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date | null;
    path: string;
    isAnonymous: boolean;
    anonimityReason: string | null;
    deleteReason: string | null;
    author: {
        id: string;
        name: {
            locale: "en" | "ru";
            value: string;
        }[];
        avatar: string | null;
    } | null;
    body: {
        locale: "en" | "ru";
        value: string;
    }[] | null;
    childrenCount: number;
    rating: {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    };
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date | null;
    path: string;
    isAnonymous: boolean;
    anonimityReason: string | null;
    deleteReason: string | null;
    author: {
        id: string;
        name: {
            locale: "en" | "ru";
            value: string;
        }[];
        avatar: string | null;
    } | null;
    body: {
        locale: "en" | "ru";
        value: string;
    }[] | null;
    childrenCount: number;
    rating: {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    };
}>;
export declare const GetCommentsResponse: z.ZodObject<{
    items: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        path: z.ZodString;
        author: z.ZodNullable<z.ZodObject<{
            id: z.ZodString;
            name: z.ZodArray<z.ZodObject<{
                locale: z.ZodEnum<["en", "ru"]>;
                value: z.ZodString;
            }, "strip", z.ZodTypeAny, {
                locale: "en" | "ru";
                value: string;
            }, {
                locale: "en" | "ru";
                value: string;
            }>, "many">;
            avatar: z.ZodNullable<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            id: string;
            name: {
                locale: "en" | "ru";
                value: string;
            }[];
            avatar: string | null;
        }, {
            id: string;
            name: {
                locale: "en" | "ru";
                value: string;
            }[];
            avatar: string | null;
        }>>;
        isAnonymous: z.ZodBoolean;
        anonimityReason: z.ZodNullable<z.ZodString>;
        rating: z.ZodObject<{
            likes: z.ZodNumber;
            dislikes: z.ZodNumber;
            status: z.ZodNullable<z.ZodNativeEnum<{
                like: "like";
                dislike: "dislike";
            }>>;
        }, "strip", z.ZodTypeAny, {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        }, {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        }>;
        body: z.ZodNullable<z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            locale: "en" | "ru";
            value: string;
        }, {
            locale: "en" | "ru";
            value: string;
        }>, "many">>;
        childrenCount: z.ZodNumber;
        deleteReason: z.ZodNullable<z.ZodString>;
        createdAt: z.ZodDate;
        updatedAt: z.ZodDate;
        deletedAt: z.ZodNullable<z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        path: string;
        isAnonymous: boolean;
        anonimityReason: string | null;
        deleteReason: string | null;
        author: {
            id: string;
            name: {
                locale: "en" | "ru";
                value: string;
            }[];
            avatar: string | null;
        } | null;
        body: {
            locale: "en" | "ru";
            value: string;
        }[] | null;
        childrenCount: number;
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
    }, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        path: string;
        isAnonymous: boolean;
        anonimityReason: string | null;
        deleteReason: string | null;
        author: {
            id: string;
            name: {
                locale: "en" | "ru";
                value: string;
            }[];
            avatar: string | null;
        } | null;
        body: {
            locale: "en" | "ru";
            value: string;
        }[] | null;
        childrenCount: number;
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
    }>, "many">;
    total: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    items: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        path: string;
        isAnonymous: boolean;
        anonimityReason: string | null;
        deleteReason: string | null;
        author: {
            id: string;
            name: {
                locale: "en" | "ru";
                value: string;
            }[];
            avatar: string | null;
        } | null;
        body: {
            locale: "en" | "ru";
            value: string;
        }[] | null;
        childrenCount: number;
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
    }[];
    total: number;
}, {
    items: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        path: string;
        isAnonymous: boolean;
        anonimityReason: string | null;
        deleteReason: string | null;
        author: {
            id: string;
            name: {
                locale: "en" | "ru";
                value: string;
            }[];
            avatar: string | null;
        } | null;
        body: {
            locale: "en" | "ru";
            value: string;
        }[] | null;
        childrenCount: number;
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
    }[];
    total: number;
}>;
export type CreateComment = z.infer<typeof CreateComment>;
export declare const CreateComment: z.ZodObject<{
    entityType: z.ZodEnum<["post", "comment"]>;
    entityId: z.ZodString;
    body: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    body: {
        locale: "en" | "ru";
        value: string;
    }[];
    entityType: "post" | "comment";
    entityId: string;
}, {
    body: {
        locale: "en" | "ru";
        value: string;
    }[];
    entityType: "post" | "comment";
    entityId: string;
}>;
export type UpdateComment = z.infer<typeof UpdateComment>;
export declare const UpdateComment: z.ZodObject<{
    body: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">>;
}, "strip", z.ZodTypeAny, {
    body?: {
        locale: "en" | "ru";
        value: string;
    }[] | undefined;
}, {
    body?: {
        locale: "en" | "ru";
        value: string;
    }[] | undefined;
}>;
export type UpdateCommentRating = z.infer<typeof UpdateCommentRating>;
export declare const UpdateCommentRating: z.ZodObject<{
    type: z.ZodNativeEnum<{
        like: "like";
        dislike: "dislike";
    }>;
}, "strip", z.ZodTypeAny, {
    type: "like" | "dislike";
}, {
    type: "like" | "dislike";
}>;
export type CommentRating = z.infer<typeof CommentRating>;
export declare const CommentRating: z.ZodObject<{
    likes: z.ZodNumber;
    dislikes: z.ZodNumber;
    status: z.ZodNullable<z.ZodNativeEnum<{
        like: "like";
        dislike: "dislike";
    }>>;
}, "strip", z.ZodTypeAny, {
    status: "like" | "dislike" | null;
    likes: number;
    dislikes: number;
}, {
    status: "like" | "dislike" | null;
    likes: number;
    dislikes: number;
}>;
export type AnonimifyComment = z.infer<typeof AnonimifyComment>;
export declare const AnonimifyComment: z.ZodObject<{
    reason: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    reason: string | null;
}, {
    reason: string | null;
}>;
export type DeleteComment = z.infer<typeof DeleteComment>;
export declare const DeleteComment: z.ZodObject<{
    reason: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    reason: string | null;
}, {
    reason: string | null;
}>;
