import type { CommentEntity } from "./comment.svelte";

export class CommentTree {
  readonly pathMap: Map<string, CommentEntity>;
  readonly parentChildrenMap: Map<string | null, CommentEntity[]>;

  constructor(comments: CommentEntity[]) {
    this.pathMap = new Map();
    this.parentChildrenMap = new Map();

    for (const comment of comments) {
      this.pathMap.set(comment.path, comment);

      const parentPath = this.getParentPath(comment.path);

      if (!this.parentChildrenMap.has(parentPath)) {
        this.parentChildrenMap.set(parentPath, []);
      }

      this.parentChildrenMap.get(parentPath)!.push(comment);
    }
  }

  getRootComments() {
    return this.getChildren(null);
  }

  getChildren(path: string | null) {
    return (
      this.parentChildrenMap.get(path)?.toSorted((a, b) => {
        if (a.isMustBeTop !== b.isMustBeTop) {
          return a.isMustBeTop ? -1 : 1;
        }

        return (b.rating.likes - b.rating.dislikes) - (a.rating.likes - a.rating.dislikes);
      }) ?? []
    );
  }

  getParentPath(path: string) {
    return path.split(".").slice(0, -1).join(".") || null;
  }

  incrementChildrenCount(path: string) {
    const segments = path.split(".");

    for (let i = 0; i < segments.length; i++) {
      const currentPath = segments.slice(0, i + 1).join(".");

      const comment = this.pathMap.get(currentPath);

      if (comment) {
        comment.childrenCount++;
      }
    }
  }
}
