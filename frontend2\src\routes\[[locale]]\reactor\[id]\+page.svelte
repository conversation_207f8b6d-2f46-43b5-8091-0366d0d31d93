<script lang="ts">
  import type { CommentEntity } from "./comment.svelte";

  import { onMount } from "svelte";
  import RightMenu from "../right-menu.svelte";
  import Comment from "./comment.svelte";
  import { CommentTree } from "./comment-tree";
  import { mockPost } from "./mock-data";
  import { fetchWithAuth } from "$lib";

  const i18n = {
    en: {
      comments: "Comments",
    },
    ru: {
      comments: "Комментарии",
    },
  };

  const { data } = $props();
  const {
    params: { id },
    locale,
  } = data;

  const t = $derived.by(() => i18n[locale]);

  // In a real implementation, we would fetch the post and comments data
  // based on the route parameter
  const post = mockPost;

  let comments = $state<CommentEntity[] | null>(null);
  const commentTree = $derived.by(() => comments && new CommentTree(comments));

  let copied = $state(false);

  $inspect(comments);
  $inspect(commentTree);

  onMount(async function fetchComments() {
    const response = await fetchWithAuth(`/api/reactor/comment?entityType=post&entityId=${id}`);

    const data = (await response.json()) as {
      items: CommentEntity[];
      total: number;
    };

    comments = data.items.map((comment) => ({
      ...comment,

      createdAt: new Date(comment.createdAt),
      updatedAt: new Date(comment.updatedAt),
      deletedAt: comment.deletedAt ? new Date(comment.deletedAt) : null,
    }));
  });

  function copyLink() {
    navigator.clipboard.writeText(`${window.location.origin}/reactor/${id}`);

    copied = true;

    setTimeout(() => (copied = false), 2000);
  }

  async function addComment(id: string) {
    console.log("addComment", {
      comments,
      id,
    });

    const response = await fetchWithAuth(`/api/reactor/comment/${id}`);

    if (response.ok) {
      const comment = await response.json();

      console.log("comment", comment);

      comments?.push({
        ...comment,

        createdAt: new Date(comment.createdAt),
        updatedAt: new Date(comment.updatedAt),
        deletedAt: comment.deletedAt ? new Date(comment.deletedAt) : null,

        isMustBeTop: true,
      });

      if (commentTree) {
        const parentPath = commentTree.getParentPath(comment.path);

        if (parentPath) {
          commentTree.incrementChildrenCount(parentPath);
        }
      }
    }
  }
</script>

<div class="row g-4 mt-3">
  <div class="col-3"></div>

  <!-- Main Content (2-9 columns) -->
  <div class="col-6">
    <div class="post-detail">
      <!-- Post Card -->
      <div class="post-card mb-4">
        <div class="card">
          <div class="card-header d-flex justify-content-between align-items-center">
            <div class="rating-block d-flex align-items-center">
              <span
                class="rating-value me-2 {post.rating > 0
                  ? 'text-success'
                  : post.rating < 0
                    ? 'text-danger'
                    : ''}"
              >
                {post.rating}
              </span>
              <div class="rating-buttons">
                <button class="btn btn-sm btn-outline-success me-1" aria-label="Like">
                  <i class="bi bi-hand-thumbs-up"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" aria-label="Dislike">
                  <i class="bi bi-hand-thumbs-down"></i>
                </button>
              </div>
            </div>

            <div class="usefulness-block">
              <div class="d-flex align-items-center">
                <div class="stars">
                  {#each Array(5) as _, i}
                    <button class="btn btn-sm p-0 px-1" aria-label={`Rate usefulness ${i + 1}`}>
                      <i class="bi bi-star{i < post.usefulness ? '-fill' : ''} text-warning"></i>
                    </button>
                  {/each}
                </div>
              </div>
            </div>
          </div>

          <div class="card-body">
            <h5 class="card-title">{post.title}</h5>

            <p class="card-text">
              {post.content}
            </p>

            <div class="tags mb-3">
              {#each post.tags as tag}
                <span class="badge bg-light text-secondary me-1">{tag}</span>
              {/each}
            </div>

            <div class="card-actions d-flex">
              <!-- <button class="btn btn-sm btn-outline-secondary me-2" aria-label="Save">
                <i class="bi bi-bookmark"></i>
              </button> -->
              <button
                class={`btn btn-sm ${copied ? "btn-success" : "btn-outline-secondary"}`}
                aria-label="Copy link"
                onclick={copyLink}
              >
                {#if copied}
                  <i class="bi bi-check-circle"></i>
                {:else}
                  <i class="bi bi-link-45deg"></i>
                {/if}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Comments Section -->
      <div class="comments-section mt-4">
        <h4 class="mb-3">{t.comments} ({comments?.length ?? 0})</h4>

        <div class="comments-list">
          {#if commentTree}
            {#each commentTree.getRootComments() as comment (comment.id)}
              <Comment {comment} {locale} expanded={true} {commentTree} {addComment} />
            {/each}
          {/if}

          <!-- {#each comments as comment (comment.id)}
            <Comment {comment} {locale} expanded={false} />
          {/each} -->
        </div>
      </div>
    </div>
  </div>

  <!-- Right Menu (10-11 columns) -->
  <div class="col-2">
    <RightMenu {locale} />
  </div>
</div>

<style>
  .post-card {
    transition: all 0.2s ease;
  }

  .rating-value {
    font-weight: bold;
    min-width: 30px;
    text-align: center;
  }

  .card-title {
    font-weight: 600;
  }

  .tags {
    margin-top: 1rem;
  }

  .badge {
    font-weight: 500;
    padding: 0.5em 0.7em;
  }

  .comments-section {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
  }
</style>
