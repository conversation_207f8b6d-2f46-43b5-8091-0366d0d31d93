import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Put,
    Query,
    UseGuards,
} from "@nestjs/common";
import { <PERSON>od<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, z } from "src/zod";
import { CurrentUser } from "src/auth/types";
import { HttpCurrentUser } from "src/auth/http/current-user.decorator";
import { HttpJwtAuthGuard } from "src/auth/http/jwt-auth.guard";
import { ReactorService } from "../reactor.service";
import * as Dto from "./dto";

@Controller("reactor")
@UseGuards(HttpJwtAuthGuard)
export class ReactorController {
    constructor(private readonly reactorService: ReactorService) {}

    @Get("post")
    async getPosts(
        @Query("page", new ZodPipe(ZodHelper.pagination.page)) page: number,
        @Query("size", new ZodPipe(ZodHelper.pagination.size)) size: number,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        const response = await this.reactorService.getPosts(
            { page, size },
            user,
        );

        return Dto.GetPostsResponse.parse(response);
    }

    @Post("post")
    async createPost(
        @Body(new ZodPipe(Dto.CreatePost)) body: Dto.CreatePost,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return this.reactorService.createPost(body, user);
    }

    @Put("post/:id")
    async updatePost(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @Body(new ZodPipe(Dto.UpdatePost)) body: Dto.UpdatePost,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return this.reactorService.updatePost(id, body, user);
    }

    @Put("post/:id/rating")
    async updatePostRating(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @Body(new ZodPipe(Dto.UpdatePostRating)) body: Dto.UpdatePostRating,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return this.reactorService.updatePostRating(id, body, user);
    }

    @Put("post/:id/usefulness")
    async updatePostUsefulness(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @Body(new ZodPipe(Dto.UpdatePostUsefulness))
        body: Dto.UpdatePostUsefulness,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return this.reactorService.updatePostUsefulness(id, body, user);
    }

    @Delete("post/:id")
    async deletePost(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @Body(new ZodPipe(Dto.DeletePost)) body: Dto.DeletePost,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return this.reactorService.deletePost(id, body, user);
    }

    @Get("comment")
    async getComments(
        @Query("entityType", new ZodPipe(Dto.GetCommentsEntityType))
        entityType: Dto.GetCommentsEntityType,
        @Query("entityId", new ZodPipe(ZodHelper.Uuid))
        entityId: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        const response = await this.reactorService.getComments(
            { entityType, entityId },
            user,
        );

        return Dto.GetCommentsResponse.parse(response);
    }

    @Get("comment/:id")
    async getComment(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        const comment = await this.reactorService.getComment({ id }, user);

        return Dto.Comment.parse(comment);
    }

    @Post("comment")
    async createComment(
        @Body(new ZodPipe(Dto.CreateComment)) body: Dto.CreateComment,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return this.reactorService.createComment(body, user);
    }

    @Put("comment/:id")
    async updateComment(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @Body(new ZodPipe(Dto.UpdateComment)) body: Dto.UpdateComment,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return this.reactorService.updateComment(id, body, user);
    }

    @Put("comment/:id/rating")
    async updateCommentRating(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @Body(new ZodPipe(Dto.UpdateCommentRating))
        body: Dto.UpdateCommentRating,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return this.reactorService.updateCommentRating(id, body, user);
    }

    @Put("comment/:id/anonimify")
    async anonimifyComment(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @Body(new ZodPipe(Dto.AnonimifyComment)) body: Dto.AnonimifyComment,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return this.reactorService.anonimifyComment(id, body, user);
    }

    @Delete("comment/:id")
    async deleteComment(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @Body(new ZodPipe(Dto.DeleteComment)) body: Dto.DeleteComment,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        return this.reactorService.deleteComment(id, body, user);
    }
}
