<script lang="ts">
  import type { Post } from "./mock-posts";
  import type { Locale } from "$lib";

  interface Props {
    post: Post;
    locale: Locale;
  }

  const i18n = {
    en: {
      showMore: "Show more",
      comments: "Comments",
      save: "Save",
      copyLink: "Copy link",
      copied: "Copied!",
      like: "Like",
      dislike: "Dislike",
    },
    ru: {
      showMore: "Показать полностью",
      comments: "Комментарии",
      save: "Сохранить",
      copyLink: "Скопировать ссылку",
      copied: "Скопировано!",
      like: "Нравится",
      dislike: "Не нравится",
    },
  };

  const { post, locale }: Props = $props();

  const t = i18n[locale];

  // State
  let expanded = $state(false);
  let copied = $state(false);

  // Constants
  const MAX_CONTENT_LENGTH = 500;
  const contentIsTruncated = post.content.length > MAX_CONTENT_LENGTH;

  // Computed
  const displayContent = $derived(
    expanded || !contentIsTruncated
      ? post.content
      : post.content.substring(0, MAX_CONTENT_LENGTH) + "...",
  );

  // Methods
  function toggleExpand() {
    expanded = !expanded;
  }

  function copyLink() {
    navigator.clipboard.writeText(`https://example.com/reactor/post/${post.id}`);
    copied = true;
    setTimeout(() => {
      copied = false;
    }, 2000);
  }

  function like() {
    // This would be implemented with actual API calls
    console.log("Like post", post.id);
  }

  function dislike() {
    // This would be implemented with actual API calls
    console.log("Dislike post", post.id);
  }

  function rateUsefulness(rating: number) {
    // This would be implemented with actual API calls
    console.log("Rate usefulness", post.id, rating);
  }
</script>

<div class="post-card mb-4">
  <div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
      <div class="rating-block d-flex align-items-center">
        <span
          class="rating-value me-2 {post.rating > 0
            ? 'text-success'
            : post.rating < 0
              ? 'text-danger'
              : ''}"
        >
          {post.rating}
        </span>
        <div class="rating-buttons">
          <button class="btn btn-sm btn-outline-success me-1" onclick={like} aria-label={t.like}>
            <i class="bi bi-hand-thumbs-up"></i>
          </button>
          <button class="btn btn-sm btn-outline-danger" onclick={dislike} aria-label={t.dislike}>
            <i class="bi bi-hand-thumbs-down"></i>
          </button>
        </div>
      </div>

      <div class="usefulness-block">
        <div class="d-flex align-items-center">
          <div class="stars">
            {#each Array(5) as _, i}
              <button
                class="btn btn-sm p-0 px-1"
                onclick={() => rateUsefulness(i + 1)}
                aria-label={`Rate usefulness ${i + 1}`}
              >
                <i class="bi bi-star{i < post.usefulness ? '-fill' : ''} text-warning"></i>
              </button>
            {/each}
          </div>
        </div>
      </div>
    </div>

    <div class="card-body">
      <h5 class="card-title">{post.title}</h5>
      <p class="card-text">
        {displayContent}
      </p>

      {#if contentIsTruncated && !expanded}
        <!-- <div class="text-gradient-overlay"></div> -->
        <button class="btn btn-sm btn-link p-0 mb-3" onclick={toggleExpand}>
          {t.showMore}
        </button>
      {/if}

      <div class="tags mb-3">
        {#each post.tags as tag}
          <span class="badge bg-light text-secondary me-1">{tag}</span>
        {/each}
      </div>

      <div class="card-actions d-flex">
        <a href={`/reactor/${post.id}#comments`} class="btn btn-sm btn-outline-secondary me-2">
          <i class="bi bi-chat-left-text me-1"></i>
          ({post.commentCount})
        </a>
        <button class="btn btn-sm btn-outline-secondary me-2" aria-label={t.save}>
          <i class="bi bi-bookmark"></i>
        </button>
        <button
          class={`btn btn-sm ${copied ? "btn-outline-success" : "btn-outline-secondary "}`}
          onclick={copyLink}
          aria-label={t.copyLink}
        >
          <i class="bi bi-link-45deg"></i>
        </button>
      </div>
    </div>
  </div>
</div>

<style>
  .post-card {
    transition: all 0.2s ease;
  }

  .rating-value {
    font-weight: bold;
    min-width: 30px;
    text-align: center;
  }

  .card-title {
    font-weight: 600;
  }

  .text-gradient-overlay {
    position: relative;
    height: 20px;
    margin-top: -20px;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
  }

  .tags {
    margin-top: 1rem;
  }

  .badge {
    font-weight: 500;
    padding: 0.5em 0.7em;
  }
</style>
