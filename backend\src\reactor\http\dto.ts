import * as prisma from "@prisma/client";
import { z, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "src/zod";

const postUsefulness = z.number().int().min(0).max(10);

export const EntityType = z.enum(["post", "comment"]);

export type GetPosts = z.infer<typeof GetPosts>;
export const GetPosts = z.object({
    pagination: z.object({
        page: ZodHelper.pagination.page,
        size: ZodHelper.pagination.size,
    }),
});

export type Post = z.infer<typeof Post>;
export const Post = z.object({
    id: ZodHelper.Uuid,

    author: z.object({
        id: ZodHelper.Uuid,
        name: ZodHelper.Localizations,
        avatar: z.string().url().nullable(),
    }),

    rating: z.object({
        likes: z.number().int().nonnegative(),
        dislikes: z.number().int().nonnegative(),
        status: z.nativeEnum(prisma.ReactorRatingType).nullable(),
    }),

    usefulness: z.object({
        value: postUsefulness.nullable(),
        count: z.number().int().nonnegative(),
        totalValue: z.number().min(0).max(10).nullable(),
    }),

    title: ZodHelper.Localizations,
    body: ZodHelper.Localizations,

    tags: z.array(ZodHelper.Uuid),

    createdAt: z.date(),
    updatedAt: z.date(),
});

export const GetPostsResponse = z.object({
    items: z.array(Post),
    total: z.number().int().nonnegative(),
});

export type CreatePost = z.infer<typeof CreatePost>;
export const CreatePost = z.object({
    title: ZodHelper.Localizations,
    body: ZodHelper.Localizations,
    tags: z.array(ZodHelper.Uuid),
});

export type UpdatePost = z.infer<typeof UpdatePost>;
export const UpdatePost = z
    .object({
        title: ZodHelper.Localizations,
        body: ZodHelper.Localizations,
        tags: z.array(ZodHelper.Uuid),
    })
    .partial();

export type UpdatePostRating = z.infer<typeof UpdatePostRating>;
export const UpdatePostRating = z.object({
    type: z.nativeEnum(prisma.ReactorRatingType),
});

export type PostRating = z.infer<typeof PostRating>;
export const PostRating = z.object({
    likes: z.number().int().nonnegative(),
    dislikes: z.number().int().nonnegative(),
    status: z.nativeEnum(prisma.ReactorRatingType).nullable(),
});

export type UpdatePostUsefulness = z.infer<typeof UpdatePostUsefulness>;
export const UpdatePostUsefulness = z.object({
    value: postUsefulness.nullable(),
});

export type PostUsefulness = z.infer<typeof PostUsefulness>;
export const PostUsefulness = z.object({
    value: postUsefulness.nullable(),
    count: z.number().int().nonnegative(),
    totalValue: z.number().min(0).max(10).nullable(),
});

export type DeletePost = z.infer<typeof DeletePost>;
export const DeletePost = z.object({
    reason: z.string().nullable(),
});

export type GetCommentsEntityType = z.infer<typeof GetCommentsEntityType>;
export const GetCommentsEntityType = z.enum(["post"]);

export type Comment = z.infer<typeof Comment>;
export const Comment = z.object({
    id: ZodHelper.Uuid,

    path: z.string().nonempty(),

    author: z
        .object({
            id: ZodHelper.Uuid,
            name: ZodHelper.Localizations,
            avatar: z.string().url().nullable(),
        })
        .nullable(),
    isAnonymous: z.boolean(),
    anonimityReason: z.string().nullable(),

    rating: z.object({
        likes: z.number().int().nonnegative(),
        dislikes: z.number().int().nonnegative(),
        status: z.nativeEnum(prisma.ReactorRatingType).nullable(),
    }),

    body: ZodHelper.Localizations.nullable(),

    childrenCount: z.number().int().nonnegative(),

    deleteReason: z.string().nonempty().nullable(),

    createdAt: z.date(),
    updatedAt: z.date(),
    deletedAt: z.date().nullable(),
});

export const GetCommentsResponse = z.object({
    items: z.array(Comment),
    total: z.number().int().nonnegative(),
});

export type CreateComment = z.infer<typeof CreateComment>;
export const CreateComment = z.object({
    entityType: EntityType,
    entityId: ZodHelper.Uuid,
    body: ZodHelper.Localizations,
});

export type UpdateComment = z.infer<typeof UpdateComment>;
export const UpdateComment = z
    .object({
        body: ZodHelper.Localizations,
    })
    .partial();

export type UpdateCommentRating = z.infer<typeof UpdateCommentRating>;
export const UpdateCommentRating = z.object({
    type: z.nativeEnum(prisma.ReactorRatingType),
});

export type CommentRating = z.infer<typeof CommentRating>;
export const CommentRating = z.object({
    likes: z.number().int().nonnegative(),
    dislikes: z.number().int().nonnegative(),
    status: z.nativeEnum(prisma.ReactorRatingType).nullable(),
});

export type AnonimifyComment = z.infer<typeof AnonimifyComment>;
export const AnonimifyComment = z.object({
    reason: z.string().nullable(),
});

export type DeleteComment = z.infer<typeof DeleteComment>;
export const DeleteComment = z.object({
    reason: z.string().nullable(),
});
